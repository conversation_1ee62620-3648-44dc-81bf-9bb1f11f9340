<script lang="ts">
	import { superForm, type SuperValidated } from 'sveltekit-superforms';
	import { zod4Client as zodClient } from 'sveltekit-superforms/adapters';
	import {
		purchaseOrderSchema,
		purchaseOrderModalSchema,
		type PurchaseOrderSchema,
		type PurchaseOrderModalSchema,
	} from '$lib/schemas/purchase_order';
	import type { VendorListItem, VendorSchema } from '$lib/schemas/vendor';
	import * as Form from '$lib/components/ui/form';
	import * as Select from '$lib/components/ui/select';
	import { Calendar } from '$lib/components/ui/calendar';
	import * as Popover from '$lib/components/ui/popover';
	import * as Dialog from '$lib/components/ui/dialog';
	import { Button, buttonVariants } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { toast } from 'svelte-sonner';
	import { Calendar as CalendarIcon } from 'phosphor-svelte';
	import {
		DateFormatter,
		getLocalTimeZone,
		parseDate,
		today,
		type DateValue,
	} from '@internationalized/date';
	import NewVendor from '$lib/components/forms/vendor/NewVendor.svelte';
	import { cn } from '$lib/utils.js';

	export interface PurchaseOrderListItem {
		purchase_order_id: string;
		po_number: string;
		description: string | null;
		po_date: string;
		vendor_id: string;
		vendor_name?: string;
		account: string | null;
		original_amount: number | null;
		co_amount: number | null;
		freight: number | null;
		tax: number | null;
		other: number | null;
		notes: string | null;
	}

	const {
		data,
		isModal = false,
		onPurchaseOrderCreated,
	}: {
		data: {
			form: SuperValidated<PurchaseOrderSchema | PurchaseOrderModalSchema>;
			newVendorForm?: SuperValidated<VendorSchema>;
			vendors: VendorListItem[];
			project: {
				project_id: string;
				name: string;
				client: {
					client_id: string;
					name: string;
					organization?: {
						org_id: string;
						name: string;
					};
				};
			};
		};
		isModal?: boolean;
		onPurchaseOrderCreated?: (purchaseOrder: PurchaseOrderListItem | null) => void;
	} = $props();

	const { vendors, project } = data;

	const form = superForm(data.form, {
		validators: zodClient(isModal ? purchaseOrderModalSchema : purchaseOrderSchema),
		onResult({ result }) {
			if (result.type === 'success' && result.data) {
				// Handle modal response with purchase order data
				if (isModal && onPurchaseOrderCreated && 'purchaseOrder' in result.data) {
					const purchaseOrder = result.data.purchaseOrder as PurchaseOrderListItem;
					onPurchaseOrderCreated(purchaseOrder);
				}
			}
		},
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: formData, enhance, validate } = form;

	let vendorList = $state<VendorListItem[]>([...vendors] as VendorListItem[]);
	let vendorDialogOpen = $state(false);

	// Date handling
	const df = new DateFormatter('default', {
		dateStyle: 'long',
	});

	let poDateValue = $derived($formData.po_date ? parseDate($formData.po_date) : undefined);
	let placeholder = $state<DateValue>(today(getLocalTimeZone()));
	let calendarOpen = $state(false);

	// Handle vendor creation from modal
	function handleVendorCreated(vendor: VendorListItem | null) {
		if (vendor) {
			// Convert VendorListItem to match the existing vendor list format
			const vendorForList = {
				vendor_id: vendor.vendor_id,
				name: vendor.name,
				description: vendor.description || '',
				vendor_type: vendor.vendor_type || '',
				contact_name: vendor.contact_name || '',
				contact_email: vendor.contact_email || '',
				contact_phone: vendor.contact_phone || '',
				is_active: vendor.is_active,
				access_level: vendor.access_level,
			};
			// Add the new vendor to the list
			vendorList = [...vendorList, vendorForList as (typeof vendorList)[0]];
			// Auto-select the new vendor
			selectedVendor = vendor.vendor_id;
			// Manually update the form data to trigger validation
			$formData.vendor_id = vendor.vendor_id;
			// Trigger validation for the vendor_id field
			validate('vendor_id');
			// Close the dialog
			vendorDialogOpen = false;
		} else {
			// Just close the dialog (cancel was clicked)
			vendorDialogOpen = false;
		}
	}

	// Vendor selection handling
	let selectedVendor = $state($formData.vendor_id || '');

	$effect(() => {
		$formData.vendor_id = selectedVendor;
	});
</script>

{#if !isModal}
	<div class="container mx-auto py-8">
		<div class="mb-6">
			<h1 class="text-2xl font-semibold">Create Purchase Order</h1>
			<p class="text-muted-foreground mt-1">
				Create a new purchase order for {project.name}
			</p>
		</div>
	</div>
{/if}

<div class="rounded-lg border p-6 shadow-sm">
	<div class="mb-6">
		<h1 class="text-2xl font-semibold">Create Purchase Order</h1>
		<p class="text-muted-foreground mt-1">
			Create a new purchase order for {project.name}
		</p>
	</div>
	<form
		method="POST"
		use:enhance
		action={isModal ? '?/createPurchaseOrderModal' : '?/createPurchaseOrder'}
	>
		<div class="space-y-6">
			<!-- Basic Information -->
			<div class="space-y-4">
				<h2 class="text-lg font-medium">Basic Information</h2>

				<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
					<Form.Field {form} name="po_number">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>PO Number <span class="text-red-500">*</span></Form.Label>
								<Input {...props} bind:value={$formData.po_number} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="po_date">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>PO Date <span class="text-red-500">*</span></Form.Label>
								<Popover.Root bind:open={calendarOpen}>
									<Popover.Trigger>
										{#snippet child({ props: triggerProps })}
											<Button
												{...props}
												{...triggerProps}
												variant="outline"
												class="w-full justify-start text-left font-normal"
											>
												<CalendarIcon class="mr-2 h-4 w-4" />
												{poDateValue
													? df.format(poDateValue.toDate(getLocalTimeZone()))
													: 'Pick a date'}
											</Button>
										{/snippet}
									</Popover.Trigger>
									<Popover.Content class="w-auto p-0" align="start">
										<Calendar
											type="single"
											value={poDateValue as DateValue}
											bind:placeholder
											captionLayout="dropdown"
											onValueChange={(v) => {
												if (v) {
													$formData.po_date = v.toString();
												} else {
													$formData.po_date = '';
												}
												calendarOpen = false;
											}}
										/>
									</Popover.Content>
								</Popover.Root>
								<input type="hidden" name="po_date" bind:value={$formData.po_date} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<Form.Field {form} name="description">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Description</Form.Label>
							<Input {...props} bind:value={$formData.description} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			</div>

			<!-- Vendor Selection -->
			<div class="space-y-4">
				<h2 class="text-lg font-medium">Vendor</h2>

				<Form.Field {form} name="vendor_id">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Vendor <span class="text-red-500">*</span></Form.Label>
							<div class="flex items-center justify-start gap-4">
								<Select.Root type="single" bind:value={selectedVendor} name={props.name}>
									<Select.Trigger {...props} class="w-3/5">
										{selectedVendor
											? vendorList.find((v) => v.vendor_id === selectedVendor)?.name
											: 'Select vendor'}
									</Select.Trigger>
									<Select.Content>
										{#each vendorList as vendor (vendor.vendor_id)}
											<Select.Item value={vendor.vendor_id}>
												{vendor.name}
											</Select.Item>
										{/each}
									</Select.Content>
								</Select.Root>

								{#if data.newVendorForm}
									<p class="shrink-0 text-center">or</p>

									<Button
										type="button"
										class={cn(buttonVariants({ variant: 'secondary', size: 'sm' }))}
										onclick={() => {
											vendorDialogOpen = true;
										}}
									>
										Create New Vendor
									</Button>
									<Dialog.Root bind:open={vendorDialogOpen}>
										<Dialog.Content class="max-h-[90vh] overflow-auto">
											<NewVendor
												isModal={true}
												onVendorCreated={handleVendorCreated}
												data={{
													form: data.newVendorForm,
													organizations: [],
													clients: [
														{
															client: project.client,
															role: 'editor',
														},
													],
													projects: [
														{
															project: {
																project_id: project.project_id,
																name: project.name,
																client: project.client,
															},
															role: 'editor',
														},
													],
												}}
											/>
										</Dialog.Content>
									</Dialog.Root>
								{/if}
							</div>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			</div>

			<!-- Financial Information -->
			<div class="space-y-4">
				<h2 class="text-lg font-medium">Financial Information</h2>

				<Form.Field {form} name="account">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Account</Form.Label>
							<Input {...props} bind:value={$formData.account} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
					<Form.Field {form} name="original_amount">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Original Amount</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									bind:value={$formData.original_amount}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="co_amount">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Change Order Amount</Form.Label>
								<Input {...props} type="number" step="0.01" bind:value={$formData.co_amount} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="freight">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Freight</Form.Label>
								<Input {...props} type="number" step="0.01" bind:value={$formData.freight} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="tax">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Tax</Form.Label>
								<Input {...props} type="number" step="0.01" bind:value={$formData.tax} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="other">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Other</Form.Label>
								<Input {...props} type="number" step="0.01" bind:value={$formData.other} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>
			</div>

			<!-- Notes -->
			<Form.Field {form} name="notes">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Notes</Form.Label>
						<Textarea {...props} bind:value={$formData.notes} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<!-- Actions -->
			<div class="flex gap-4">
				<Button type="submit">
					{isModal ? 'Create Purchase Order' : 'Create Purchase Order'}
				</Button>
				{#if isModal && onPurchaseOrderCreated}
					<Button type="button" variant="outline" onclick={() => onPurchaseOrderCreated?.(null)}>
						Cancel
					</Button>
				{/if}
			</div>
		</div>
	</form>
</div>
