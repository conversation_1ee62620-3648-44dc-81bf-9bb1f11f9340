@import 'tailwindcss';
@import 'tw-animate-css';

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentcolor);
	}
}

/* default */
/* :root {
		--background: hsl(0 0% 100%);
		--foreground: hsl(20 14.3% 4.1%);
		--muted: hsl(60 4.8% 95.9%);
		--muted-foreground: hsl(25 5.3% 44.7%);
		--popover: hsl(0 0% 100%);
		--popover-foreground: hsl(20 14.3% 4.1%);
		--card: hsl(0 0% 100%);
		--card-foreground: hsl(20 14.3% 4.1%);
		--border: hsl(20 5.9% 90%);
		--input: hsl(20 5.9% 90%);
		--primary: hsl(24 9.8% 10%);
		--primary-foreground: hsl(60 9.1% 97.8%);
		--secondary: hsl(60 4.8% 95.9%);
		--secondary-foreground: hsl(24 9.8% 10%);
		--accent: hsl(60 4.8% 95.9%);
		--accent-foreground: hsl(24 9.8% 10%);
		--destructive: hsl(0 72.2% 50.6%);
		--destructive-foreground: hsl(60 9.1% 97.8%);
		--ring: hsl(20 14.3% 4.1%);
		--radius: 0.5rem;
		--sidebar-background: hsl(0 0% 98%);
		--sidebar-foreground: hsl(240 5.3% 26.1%);
		--sidebar-primary: hsl(240 5.9% 10%);
		--sidebar-primary-foreground: hsl(0 0% 98%);
		--sidebar-accent: hsl(240 4.8% 95.9%);
		--sidebar-accent-foreground: hsl(240 5.9% 10%);
		--sidebar-border: hsl(220 13% 91%);
		--sidebar-ring: hsl(217.2 91.2% 59.8%);
	}

	.dark {
		--background: hsl(20 14.3% 4.1%);
		--foreground: hsl(60 9.1% 97.8%);
		--muted: hsl(12 6.5% 15.1%);
		--muted-foreground: hsl(24 5.4% 63.9%);
		--popover: hsl(20 14.3% 4.1%);
		--popover-foreground: hsl(60 9.1% 97.8%);
		--card: hsl(20 14.3% 4.1%);
		--card-foreground: hsl(60 9.1% 97.8%);
		--border: hsl(12 6.5% 15.1%);
		--input: hsl(12 6.5% 15.1%);
		--primary: hsl(60 9.1% 97.8%);
		--primary-foreground: hsl(24 9.8% 10%);
		--secondary: hsl(12 6.5% 15.1%);
		--secondary-foreground: hsl(60 9.1% 97.8%);
		--accent: hsl(12 6.5% 15.1%);
		--accent-foreground: hsl(60 9.1% 97.8%);
		--destructive: hsl(0 62.8% 30.6%);
		--destructive-foreground: hsl(60 9.1% 97.8%);
		--ring: hsl(24 5.7% 82.9%);
		--sidebar-background: hsl(240 5.9% 10%);
		--sidebar-foreground: hsl(240 4.8% 95.9%);
		--sidebar-primary: hsl(224.3 76.3% 48%);
		--sidebar-primary-foreground: hsl(0 0% 100%);
		--sidebar-accent: hsl(240 3.7% 15.9%);
		--sidebar-accent-foreground: hsl(240 4.8% 95.9%);
		--sidebar-border: hsl(240 3.7% 15.9%);
		--sidebar-ring: hsl(217.2 91.2% 59.8%);
	} */

/* aurora teal */
:root {
	--background: hsl(158 74% 100%);
	--foreground: hsl(158 5% 10%);
	--card: hsl(158 50% 99%);
	--card-foreground: hsl(158 5% 15%);
	--popover: hsl(158 74% 99%);
	--popover-foreground: hsl(158 95% 10%);
	--primary: hsl(158 69% 65.9%);
	--primary-foreground: hsl(158 69% 25%);
	--secondary: hsl(158 30% 90%);
	--secondary-foreground: hsl(0 0% 0%);
	--muted: hsl(120 30% 95%);
	--muted-foreground: hsl(158 5% 40%);
	--accent: hsl(120 30% 90%);
	--accent-foreground: hsl(158 5% 15%);
	--destructive: hsl(0 74% 50%);
	--destructive-foreground: hsl(158 5% 99%);
	--border: hsl(158 30% 82%);
	--input: hsl(158 30% 50%);
	--ring: hsl(158 69% 65.9%);
	--sidebar: hsl(0 0% 98%);
	--sidebar-background: hsl(0 0% 98%);
	--sidebar-foreground: hsl(240 5.3% 26.1%);
	--sidebar-primary: hsl(240 5.9% 10%);
	--sidebar-primary-foreground: hsl(0 0% 98%);
	--sidebar-accent: hsl(240 4.8% 95.9%);
	--sidebar-accent-foreground: hsl(240 5.9% 10%);
	--sidebar-border: hsl(220 13% 91%);
	--sidebar-ring: hsl(158 91.2% 59.8%);

	--radius: 0.3rem;
}
.dark {
	--background: hsl(158 50% 10%);
	--foreground: hsl(158 5% 99%);
	--card: hsl(158 50% 10%);
	--card-foreground: hsl(158 5% 99%);
	--popover: hsl(158 50% 5%);
	--popover-foreground: hsl(158 5% 99%);
	--primary: hsl(158 69% 65.9%);
	--primary-foreground: hsl(0 0% 0%);
	--secondary: hsl(158 30% 20%);
	--secondary-foreground: hsl(0 0% 100%);
	--muted: hsl(120 30% 25%);
	--muted-foreground: hsl(158 5% 65%);
	--accent: hsl(120 30% 25%);
	--accent-foreground: hsl(158 5% 95%);
	--destructive: hsl(0 74% 50%);
	--destructive-foreground: hsl(158 5% 99%);
	--border: hsl(158 30% 50%);
	--input: hsl(158 30% 50%);
	--ring: hsl(158 69% 65.9%);
	--sidebar: hsl(240 5.9% 10%);
	--sidebar-background: hsl(240 5.9% 10%);
	--sidebar-foreground: hsl(240 4.8% 95.9%);
	--sidebar-primary: hsl(158 76.3% 48%);
	--sidebar-primary-foreground: hsl(0 0% 100%);
	--sidebar-accent: hsl(240 3.7% 15.9%);
	--sidebar-accent-foreground: hsl(240 4.8% 95.9%);
	--sidebar-border: hsl(240 3.7% 15.9%);
	--sidebar-ring: hsl(158 91.2% 59.8%);

	--radius: 0.3rem;
}

@theme inline {
	/* Radius (for rounded-*) */
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);

	/* Colors */
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-ring: var(--ring);
	--color-radius: var(--radius);
	--color-sidebar: var(--sidebar);
	--color-sidebar-background: var(--sidebar-background);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);

	/* Animations */
	--animate-accordion-up: accordion-up 0.2s ease-out;
	--animate-accordion-down: accordion-down 0.2s ease-out;
	--animate-caret-blink: caret-blink 1.25s ease-out infinite;
}

@layer base {
	html,
	body {
		scrollbar-gutter: stable;
	}

	* {
		@apply border-border;
	}

	body {
		@apply bg-background text-foreground;
	}

	h1 {
		@apply mb-6 text-3xl font-bold;
	}
}

@utility container {
	margin-inline: auto;
	padding-inline: 2rem;
}

@keyframes accordion-down {
	from {
		height: 0;
	}

	to {
		height: var(--bits-accordion-content-height);
	}
}

@keyframes accordion-up {
	from {
		height: var(--bits-accordion-content-height);
	}

	to {
		height: 0;
	}
}

@keyframes caret-blink {
	0%,
	70%,
	100% {
		opacity: 1;
	}

	20%,
	50% {
		opacity: 0;
	}
}

:is([data-state='expanded'], [data-mobile='true']) [data-sidebar='header'] a svg {
	@apply size-12;
}

[data-state='collapsed'] [data-sidebar='header'] a svg {
	@apply size-7;
}

nav[aria-label='breadcrumb'] ol li:last-child a {
	@apply text-foreground hover:underline;
}

header button[data-sidebar='trigger'] svg {
	@apply size-6;
}

td button svg {
	@apply size-5;
}
.grid-cols-12 input[type='number']::-webkit-outer-spin-button,
.grid-cols-12 input[type='number']::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}
.grid-cols-12 input[type='number'] {
	font-variant-numeric: tabular-nums;
	margin: 0 0.25rem;
	text-align: right;
}

ul[data-slot='sidebar-menu'] {
	@apply space-y-2;
}
