create extension if not exists "pgjwt"
with
	schema "extensions";

drop policy "Users can view custom WBS library item they have access to" on "public"."wbs_library_item";

drop policy "Users can view standard WBS library item" on "public"."wbs_library_item";

drop policy "Admins and the invitee can update invites" on "public"."invite";

drop policy "Users can create organizations" on "public"."organization";

drop policy "Users can create vendors at levels they have admin access to" on "public"."vendor";

alter table "public"."work_package"
drop constraint "work_package_parent_work_package_id_fkey";

drop function if exists "public"."get_work_packages_for_parent_selection" (
	project_id_param uuid,
	exclude_work_package_id uuid
);

drop function if exists "public"."get_accessible_work_packages" (project_id_param uuid);

drop index if exists "public"."work_package_parent_work_package_id_idx";

alter table "public"."work_package"
drop column "parent_work_package_id";

alter table "public"."work_package_audit"
drop column "parent_work_package_id";

set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.audit_work_package_changes () RETURNS trigger LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.work_package_audit (
            operation_type, changed_by, changed_at, old_values,
            work_package_id, name, description, project_id,
            purchase_order_id, wbs_library_item_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.work_package_id, OLD.name, OLD.description, OLD.project_id, 
            OLD.purchase_order_id, OLD.wbs_library_item_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.work_package_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            work_package_id, name, description, project_id, 
            purchase_order_id, wbs_library_item_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.work_package_id, NEW.name, NEW.description, NEW.project_id, 
            NEW.purchase_order_id, NEW.wbs_library_item_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.work_package_audit (
            operation_type, changed_by, changed_at, new_values,
            work_package_id, name, description, project_id, 
            purchase_order_id, wbs_library_item_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.work_package_id, NEW.name, NEW.description, NEW.project_id, 
            NEW.purchase_order_id, NEW.wbs_library_item_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$;

CREATE OR REPLACE FUNCTION public.get_accessible_work_packages (project_id_param uuid) RETURNS TABLE (
	work_package_id uuid,
	name text,
	description text,
	project_id uuid,
	purchase_order_id uuid,
	purchase_order_number text,
	wbs_library_item_id uuid,
	wbs_code text,
	wbs_description text,
	created_at timestamp with time zone,
	updated_at timestamp with time zone
) LANGUAGE plpgsql
SET
	search_path TO '' AS $function$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RETURN;
	END IF;

	-- Return work packages for the project with related information
	RETURN QUERY
	SELECT
		wp.work_package_id,
		wp.name,
		wp.description,
		wp.project_id,
		wp.purchase_order_id,
		po.po_number AS purchase_order_number,
		wp.wbs_library_item_id,
		wli.code AS wbs_code,
		wli.description AS wbs_description,
		wp.created_at,
		wp.updated_at
	FROM public.work_package wp
	LEFT JOIN public.purchase_order po ON wp.purchase_order_id = po.purchase_order_id
	LEFT JOIN public.wbs_library_item wli ON wp.wbs_library_item_id = wli.wbs_library_item_id
	WHERE wp.project_id = project_id_param
	ORDER BY wp.name;
END;
$function$;

create policy "Users can view WBS library items" on "public"."wbs_library_item" as permissive for
select
	to authenticated using (
		(
			(item_type = 'Standard'::wbs_item_type)
			OR (
				(item_type = 'Custom'::wbs_item_type)
				AND (
					current_user_has_entity_access ('client'::entity_type, client_id)
					OR (
						(project_id IS NOT NULL)
						AND current_user_has_entity_access ('project'::entity_type, project_id)
					)
				)
			)
		)
	);

create policy "Admins and the invitee can update invites" on "public"."invite" as permissive
for update
	to authenticated using (
		(
			(
				(
					resource_type = 'organization'::invite_resource_type
				)
				AND current_user_has_entity_role (
					'organization'::entity_type,
					resource_id,
					'admin'::membership_role
				)
			)
			OR (
				(resource_type = 'client'::invite_resource_type)
				AND current_user_has_entity_role (
					'client'::entity_type,
					resource_id,
					'admin'::membership_role
				)
			)
			OR (
				(resource_type = 'project'::invite_resource_type)
				AND can_modify_project (resource_id)
			)
			OR (
				EXISTS (
					SELECT
						1
					FROM
						profile p
					WHERE
						(
							(
								p.user_id = (
									SELECT
										auth.uid () AS uid
								)
							)
							AND (p.email = invite.invitee_email)
						)
				)
			)
		)
	)
with
	check (
		(
			(
				(
					resource_type = 'organization'::invite_resource_type
				)
				AND current_user_has_entity_role (
					'organization'::entity_type,
					resource_id,
					'admin'::membership_role
				)
			)
			OR (
				(resource_type = 'client'::invite_resource_type)
				AND current_user_has_entity_role (
					'client'::entity_type,
					resource_id,
					'admin'::membership_role
				)
			)
			OR (
				(resource_type = 'project'::invite_resource_type)
				AND can_modify_project (resource_id)
			)
			OR (
				EXISTS (
					SELECT
						1
					FROM
						profile p
					WHERE
						(
							(
								p.user_id = (
									SELECT
										auth.uid () AS uid
								)
							)
							AND (p.email = invite.invitee_email)
						)
				)
			)
		)
	);

create policy "Users can create organizations" on "public"."organization" as permissive for insert to authenticated
with
	check (
		(
			created_by_user_id = (
				SELECT
					auth.uid () AS uid
			)
		)
	);

create policy "Users can create vendors at levels they have admin access to" on "public"."vendor" as permissive for insert to authenticated
with
	check (
		(
			(
				(
					(org_id IS NOT NULL)
					AND current_user_has_entity_role (
						'organization'::entity_type,
						org_id,
						'admin'::membership_role
					)
				)
				OR (
					(client_id IS NOT NULL)
					AND current_user_has_entity_role (
						'client'::entity_type,
						client_id,
						'admin'::membership_role
					)
				)
				OR (
					(project_id IS NOT NULL)
					AND current_user_has_entity_role (
						'project'::entity_type,
						project_id,
						'editor'::membership_role
					)
				)
			)
			AND (
				created_by_user_id = (
					SELECT
						auth.uid () AS uid
				)
			)
		)
	);
